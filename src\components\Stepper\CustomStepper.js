import * as React from "react";
import Box from "@mui/material/Box";
import Stepper from "@mui/material/Stepper";
import Step from "@mui/material/Step";
import StepLabel from "@mui/material/StepLabel";
import Tooltip from "@mui/material/Tooltip";
import { styled } from "@mui/material/styles";
import { useTranslation } from "react-i18next";
import { CheckCircle, RadioIcon, GreyCircleIcon } from "../../icons";
import { StepConnector } from "@mui/material";

// Ultra-modern glassmorphism tooltip with advanced animations
const StepInfoTooltip = styled(({ className, ...props }) => (
  <Tooltip {...props} classes={{ popper: className }} />
))(({ theme }) => ({
  [`& .MuiTooltip-tooltip`]: {
    background: `
      linear-gradient(135deg,
        rgba(15, 23, 42, 0.95) 0%,
        rgba(30, 41, 59, 0.9) 50%,
        rgba(51, 65, 85, 0.85) 100%
      )
    `,
    backdropFilter: "blur(20px) saturate(180%)",
    WebkitBackdropFilter: "blur(20px) saturate(180%)",
    border: "1px solid rgba(148, 163, 184, 0.3)",
    borderRadius: "16px",
    padding: "0",
    maxWidth: 320,
    minWidth: 280,
    fontSize: "13px",
    fontFamily: "OpenSanHebrew",
    lineHeight: 1.5,
    overflow: "hidden",
    position: "relative",
    boxShadow: `
      0 25px 50px -12px rgba(0, 0, 0, 0.4),
      0 0 0 1px rgba(255, 255, 255, 0.05),
      inset 0 1px 0 rgba(255, 255, 255, 0.1)
    `,
    "&::before": {
      content: '""',
      position: "absolute",
      top: 0,
      left: 0,
      right: 0,
      height: "1px",
      background:
        "linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.5), transparent)",
    },
    "&::after": {
      content: '""',
      position: "absolute",
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background: `
        radial-gradient(circle at 50% 0%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 100% 50%, rgba(147, 51, 234, 0.05) 0%, transparent 50%)
      `,
      pointerEvents: "none",
    },
  },
  [`& .MuiTooltip-arrow`]: {
    color: "rgba(30, 41, 59, 0.9)",
    filter: "drop-shadow(0 2px 8px rgba(0, 0, 0, 0.3))",
    "&::before": {
      border: "1px solid rgba(148, 163, 184, 0.3)",
      background: `
        linear-gradient(135deg,
          rgba(30, 41, 59, 0.9) 0%,
          rgba(51, 65, 85, 0.85) 100%
        )
      `,
      backdropFilter: "blur(20px)",
    },
  },
}));

// Enhanced tooltip content with premium card design and animations
const renderTooltipContent = (stepperData, index) => {
  const data = stepperData?.[index];

  const stepTitles = {
    0: "General Information",
    1: "MNP Information",
    2: "Tadig Information",
    3: "Table Information",
  };

  if (data) {
    return (
      <div className="relative p-5">
        {/* Header without background light */}
        <div className="relative mb-4 pb-4">
          <div className="relative flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div
                className={`text-sm font-medium bg-gradient-to-r from-blue-500 to-cyan-500 bg-clip-text text-transparent`}
              >
                {stepTitles[index]}
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced content grid */}
        <div className="space-y-2">
          {Object.entries(data).map(([key, value], idx) => (
            <div
              key={key}
              className="relative p-2 rounded-lg border border-slate-700/50 bg-slate-800/30 backdrop-blur-sm"
            >
              <div className="flex items-center space-x-2 mb-1">
                <div className="w-1 h-4 bg-gradient-to-b from-blue-400 to-purple-500 rounded-full"></div>
                <div className="text-slate-400 text-xs font-semibold uppercase tracking-wider">
                  {key.replace(/([A-Z])/g, " $1").trim()}
                </div>
              </div>
              <div className="text-white text-sm font-medium ml-3">
                {value || (
                  <span className="text-slate-500 italic">Not provided</span>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  // Enhanced sample data with more realistic values
  const sampleData = {
    0: {
      "Operator Name": "Operator_123",
      "Country Name": "Country123",
      Region: "North America",
      Status: "Active",
    },
    1: {
      "MNP Status": "✓ MNP correction enabled",
      Gateway: "Primary Gateway",
      Protocol: "SS7/Diameter",
      Capacity: "10K TPS",
    },
    2: {
      "TADIG Code": "ABC123",
      "Country Initials": "US",
      "Network Type": "GSM/UMTS/LTE",
      Roaming: "Enabled",
    },
  };

  const stepData = sampleData[index];
  if (!stepData) return null;

  return (
    <div className="relative p-2">
      {/* Header without background light */}
      <div className="relative mb-2">
        <div
          className={`text-base font-bold bg-gradient-to-r from-blue-500 to-cyan-500 bg-clip-text text-transparent`}
        >
          {stepTitles[index]}
        </div>
        <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-slate-600 to-transparent"></div>
      </div>

      {/* Content grid */}
      <div className="space-y-4">
        {Object.entries(stepData).map(([key, value], idx) => (
          <div key={key} className="group relative">
            <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-300 transform group-hover:scale-105"></div>

            <div className="relative p-3 rounded-lg border border-slate-700/50 bg-slate-800/30 backdrop-blur-sm transition-all duration-300 group-hover:border-slate-600/70 group-hover:shadow-lg">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <div className="w-1 h-4 bg-gradient-to-b from-blue-400 to-purple-500 rounded-full"></div>
                    <div className="text-slate-400 text-xs font-semibold uppercase tracking-wider">
                      {key.replace(/([A-Z])/g, " $1").trim()}
                    </div>
                  </div>
                  <div className="text-white text-sm font-medium leading-relaxed ml-3">
                    {value || (
                      <span className="text-slate-500 italic">
                        Not provided
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

function CustomStepper(props) {
  const { t } = useTranslation();
  console.log("CustomStepper props", props);
  return (
    <Box className="bg-[#374150] flex flex-col min-w-[250px] max-w-[250px] h-[650px] rounded-l-lg p-4 max-h-[76vh] overflow-y-auto sticky top-0">
      <Stepper
        activeStep={props.atStep}
        orientation="vertical"
        sx={{
          width: "100%",
          marginTop: "24px",
          ".MuiStepConnector-root": {
            flex: "unset !important",
          },
        }}
        connector={
          <StepConnector
            sx={{
              "& .MuiStepConnector-lineVertical": {
                minHeight: "7vh",
                marginLeft: "164px",
              },
            }}
          />
        }
      >
        {props.steps.map((label, index) => (
          <Step
            key={label}
            sx={{
              ".MuiStepper-vertical": {
                padding: "10px !important",
                marginTop: "0px",
                marginBottom: "0px",
              },
              "& .MuiStepLabel-root .Mui-active .MuiStepIcon-root": {
                color: "#ffffff",
              },
              "& .MuiStepLabel-root .Mui-active .MuiStepIcon-text": {
                display: "none",
              },
              "& .MuiStepLabel-root .Mui-active .MuiStepIcon-completed": {
                display: "block",
              },
              "& .MuiStepLabel-root .Mui-completed .MuiStepIcon-root": {
                color: "white",
              },
              "& .MuiStepLabel-root .Mui-completed .MuiStepIcon-text": {
                display: "none",
              },
              "& .MuiStepLabel-root .Mui-completed .MuiStepIcon-completed": {
                display: "block",
              },
              "& .MuiStepIcon-root": {
                width: 30,
                height: 30,
              },
              "& .MuiStepLabel-root": {
                padding: 0,
                width: "220px",
              },
              "& .MuiStepLabel-iconContainer": {
                order: 2,
                marginLeft: "10px",
              },
              "& .MuiStepLabel-labelContainer": {
                width: "70%",
                padding: "10px",
              },
              "& .MuiStepLabel-label": {
                wordBreak: "break-word",
                color: "#9C9898",
              },
              "& .MuiStepLabel-label.Mui-active": {
                wordBreak: "break-word",
                color: "white",
              },
              "& .MuiStepLabel-label.Mui-completed": {
                wordBreak: "break-word",
                color: "white",
              },
              "& .MuiStep-root .MuiStepLabel-label": {
                wordBreak: "break-word",
              },
            }}
          >
            <StepLabel
              StepIconComponent={() => {
                const IconComponent = () => {
                  if (index === props.atStep) {
                    return <RadioIcon style={{ marginLeft: "-2px" }} />;
                  } else if (index < props.atStep) {
                    return <CheckCircle />;
                  } else {
                    return <GreyCircleIcon style={{ marginLeft: "4px" }} />;
                  }
                };

                // Enhanced tooltip for completed steps with new interactive design
                if (index < props.atStep) {
                  return (
                    <StepInfoTooltip
                      title={renderTooltipContent(props.stepperData, index)}
                      arrow
                      placement="right"
                      enterDelay={200}
                      leaveDelay={300}
                      TransitionProps={{
                        timeout: {
                          enter: 300,
                          exit: 200,
                        },
                      }}
                    >
                      <div
                        style={{
                          cursor: "pointer",
                          position: "relative",
                          transition: "all 0.4s cubic-bezier(0.4, 0, 0.2, 1)",
                        }}
                        className="hover:scale-125 group relative transform-gpu"
                      >
                        <IconComponent />

                        {/* Multi-layered glow effects */}
                        <div className="absolute -inset-3 bg-gradient-to-r from-blue-400 via-purple-500 to-cyan-400 rounded-full opacity-0 group-hover:opacity-30 transition-all duration-700 animate-pulse blur-sm"></div>
                        <div className="absolute -inset-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full opacity-0 group-hover:opacity-40 transition-all duration-500 blur-xs"></div>
                        <div className="absolute -inset-1 bg-blue-400 rounded-full opacity-10 group-hover:opacity-50 transition-all duration-400"></div>

                        {/* Premium tooltip indicator with micro-interactions */}
                        <div className="absolute -top-1.5 -right-1.5 w-4 h-4 bg-gradient-to-br from-emerald-400 via-blue-500 to-purple-600 rounded-full opacity-90 group-hover:opacity-100 transition-all duration-300 shadow-xl transform group-hover:scale-110">
                          <div className="absolute inset-0.5 bg-white rounded-full opacity-40 animate-ping"></div>
                          <div className="absolute inset-1 bg-white rounded-full opacity-60"></div>
                          <div
                            className="absolute inset-0 rounded-full border border-white/30 animate-spin"
                            style={{ animation: "spin 3s linear infinite" }}
                          ></div>
                        </div>

                        {/* Floating particles effect */}
                        <div className="absolute inset-0 pointer-events-none">
                          <div
                            className="absolute w-1 h-1 bg-blue-400 rounded-full opacity-0 group-hover:opacity-100 animate-bounce"
                            style={{
                              top: "-8px",
                              left: "50%",
                              animationDelay: "0s",
                            }}
                          ></div>
                          <div
                            className="absolute w-1 h-1 bg-purple-400 rounded-full opacity-0 group-hover:opacity-100 animate-bounce"
                            style={{
                              top: "-6px",
                              right: "-4px",
                              animationDelay: "0.2s",
                            }}
                          ></div>
                          <div
                            className="absolute w-1 h-1 bg-cyan-400 rounded-full opacity-0 group-hover:opacity-100 animate-bounce"
                            style={{
                              bottom: "-6px",
                              left: "-4px",
                              animationDelay: "0.4s",
                            }}
                          ></div>
                        </div>
                      </div>
                    </StepInfoTooltip>
                  );
                }

                return <IconComponent />;
              }}
            >
              <div
                className="flex flex-col"
                style={{ fontFamily: "OpenSanHebrew" }}
              >
                <b className="text-end">{`Step ${index + 1}`} </b>
                <div className="text-end whitespace-nowrap"> {t(label)}</div>
              </div>
            </StepLabel>
          </Step>
        ))}
      </Stepper>
    </Box>
  );
}

export default CustomStepper;
